import os
import pandas as pd
from datetime import datetime, timedelta
from openpyxl import load_workbook
import xlrd
from typing import List, Tuple, Dict, Any, Optional
import warnings

# 忽略警告信息
warnings.filterwarnings('ignore')


class ExcelHeaderProcessor:
    """
    Excel表头处理器类
    用于分析Excel文件的表头类型，处理合并单元格，并提取表头信息
    """

    def __init__(self):
        """初始化处理器"""
        pass

    def _get_merged_cell_value_openpyxl(self, worksheet, row: int, col: int) -> Any:
        """
        使用openpyxl获取合并单元格的值

        Args:
            worksheet: openpyxl工作表对象
            row: 行号（1开始）
            col: 列号（1开始）

        Returns:
            单元格的值，如果是合并单元格则返回合并区域的值
        """
        try:
            cell = worksheet.cell(row=row, column=col)

            # 检查当前单元格是否在合并区域中
            for merged_range in worksheet.merged_cells.ranges:
                if cell.coordinate in merged_range:
                    # 返回合并区域左上角单元格的值
                    top_left_cell = worksheet.cell(
                        row=merged_range.min_row,
                        column=merged_range.min_col
                    )
                    return top_left_cell.value

            # 如果不是合并单元格，直接返回单元格值
            return cell.value
        except Exception:
            return None

    def _get_merged_cell_value_xlrd(self, sheet, row: int, col: int) -> Any:
        """
        使用xlrd获取合并单元格的值（用于处理.xls文件）

        Args:
            sheet: xlrd工作表对象
            row: 行号（0开始）
            col: 列号（0开始）

        Returns:
            单元格的值，如果是合并单元格则返回合并区域的值
        """
        try:
            # 检查当前单元格是否在合并区域中
            for merged_range in sheet.merged_cells:
                rlo, rhi, clo, chi = merged_range
                if rlo <= row < rhi and clo <= col < chi:
                    # 返回合并区域左上角单元格的值
                    return sheet.cell_value(rlo, clo)

            # 如果不是合并单元格，直接返回单元格值
            return sheet.cell_value(row, col)
        except Exception:
            return None

    def _read_excel_with_merged_cells(self, file_path: str, max_rows: int = 5) -> Tuple[List[List], int]:
        """
        读取Excel文件并处理合并单元格

        Args:
            file_path: Excel文件路径
            max_rows: 最大读取行数，默认为5行

        Returns:
            Tuple[List[List], int]: (处理后的数据列表, 总列数)
            数据列表中每个元素是一行数据的列表
        """
        try:
            # 根据文件扩展名选择不同的处理方式
            if file_path.lower().endswith('.xlsx'):
                # 处理.xlsx文件
                workbook = load_workbook(file_path, data_only=True)
                worksheet = workbook.active

                # 获取工作表的最大行数和列数
                max_row = min(worksheet.max_row, max_rows)
                max_col = worksheet.max_column

                data = []
                # 逐行读取数据
                for row in range(1, max_row + 1):
                    row_data = []
                    for col in range(1, max_col + 1):
                        # 获取单元格值（处理合并单元格）
                        value = self._get_merged_cell_value_openpyxl(worksheet, row, col)
                        row_data.append(value)
                    data.append(row_data)

                workbook.close()
                return data, max_col

            else:
                # 处理.xls文件
                workbook = xlrd.open_workbook(file_path)
                sheet = workbook.sheet_by_index(0)

                # 获取工作表的行数和列数
                max_row = min(sheet.nrows, max_rows)
                max_col = sheet.ncols

                data = []
                # 逐行读取数据
                for row in range(max_row):
                    row_data = []
                    for col in range(max_col):
                        # 获取单元格值（处理合并单元格）
                        value = self._get_merged_cell_value_xlrd(sheet, row, col)
                        row_data.append(value)
                    data.append(row_data)

                return data, max_col

        except Exception as e:
            print(f"读取文件 {file_path} 时出错: {str(e)}")
            return [], 0

    def _is_numeric_value(self, value: Any) -> bool:
        """
        判断值是否为数值型

        Args:
            value: 要判断的值

        Returns:
            bool: 如果是数值型返回True，否则返回False
        """
        if value is None:
            return False

        # 如果已经是数值类型
        if isinstance(value, (int, float)):
            return True

        # 如果是字符串，尝试转换为数值
        if isinstance(value, str):
            try:
                float(value.strip())
                return True
            except (ValueError, AttributeError):
                return False

        return False

    def _analyze_row_characteristics(self, row_data: List) -> Dict[str, float]:
        """
        分析行数据的特征

        Args:
            row_data: 一行的数据列表

        Returns:
            Dict[str, float]: 包含各种特征比例的字典
            - null_ratio: 空值比例
            - numeric_ratio: 数值型比例
            - text_ratio: 文本型比例
            - total_cells: 总单元格数
        """
        if not row_data:
            return {
                'null_ratio': 1.0,
                'numeric_ratio': 0.0,
                'text_ratio': 0.0,
                'total_cells': 0
            }

        total_cells = len(row_data)
        null_count = 0  # 空值计数
        numeric_count = 0  # 数值型计数
        text_count = 0  # 文本型计数

        for value in row_data:
            if value is None or (isinstance(value, str) and value.strip() == ''):
                null_count += 1
            elif self._is_numeric_value(value):
                numeric_count += 1
            else:
                text_count += 1

        return {
            'null_ratio': null_count / total_cells,
            'numeric_ratio': numeric_count / total_cells,
            'text_ratio': text_count / total_cells,
            'total_cells': total_cells
        }

    def _is_header_row(self, row_data: List, row_index: int = 0) -> bool:
        """
        判断一行是否为表头行

        改进的判断标准：
        1. 基础条件：空值率 < 30%，数值型比例 < 50%，文本比例 > 60%
        2. 位置权重：第一行更可能是表头
        3. 内容特征：表头通常更简洁，避免复合词汇
        4. 重复性检查：数据行通常包含更多重复内容

        Args:
            row_data: 一行的数据列表
            row_index: 行索引，用于位置权重计算

        Returns:
            bool: 如果是表头行返回True，否则返回False
        """
        characteristics = self._analyze_row_characteristics(row_data)

        # 基础条件检查
        basic_conditions = (
                characteristics['null_ratio'] < 0.3 and  # 空值率小于30%
                characteristics['numeric_ratio'] < 0.5 and  # 数值型比例小于50%
                characteristics['text_ratio'] > 0.6  # 文本比例大于60%
        )

        if not basic_conditions:
            return False

        # 位置权重：第一行更可能是表头
        if row_index == 0:
            return True

        # 内容特征分析：检查是否包含过多复合内容
        complex_content_count = 0
        total_text_cells = 0

        for value in row_data:
            if value is not None and isinstance(value, str) and value.strip():
                text = value.strip()
                total_text_cells += 1

                # 检查是否包含复合内容（如"语文数学"、"地理英语"等）
                # 这类内容更可能出现在数据行而不是表头行
                if len(text) > 4 or any(char in text for char in ['、', '，', ',', ' ']):
                    complex_content_count += 1

        # 如果复合内容比例过高，不太可能是表头行
        if total_text_cells > 0:
            complex_ratio = complex_content_count / total_text_cells
            if complex_ratio > 0.5:  # 超过50%的单元格包含复合内容
                return False

        return True

    def _validate_multi_row_header(self, data: List[List], header_rows: List[int]) -> bool:
        """
        验证多行表头的合理性

        验证标准：
        1. 多行表头应该有层次关系或补充关系
        2. 后续行不应该包含明显的数据特征
        3. 多行表头的内容应该相对简洁

        Args:
            data: Excel数据
            header_rows: 候选表头行索引列表

        Returns:
            bool: 如果是合理的多行表头返回True，否则返回False
        """
        if len(header_rows) < 2:
            return False

        # 检查第二行及后续行是否包含明显的数据特征
        for i in range(1, len(header_rows)):
            row_idx = header_rows[i]
            row_data = data[row_idx]

            # 计算该行的复合内容比例
            complex_content_count = 0
            total_text_cells = 0

            for value in row_data:
                if value is not None and isinstance(value, str) and value.strip():
                    text = value.strip()
                    total_text_cells += 1

                    # 检查是否包含明显的数据特征
                    # 1. 长度过长（可能是复合数据）
                    # 2. 包含分隔符
                    # 3. 包含明显的课程组合等数据特征
                    if (len(text) > 6 or
                        any(char in text for char in ['、', '，', ',', ' ', '/', '\\']) or
                        self._contains_data_patterns(text)):
                        complex_content_count += 1

            # 如果复合内容比例过高，不太可能是表头行
            if total_text_cells > 0:
                complex_ratio = complex_content_count / total_text_cells
                if complex_ratio > 0.3:  # 超过30%的单元格包含复合内容
                    return False

        return True

    def _contains_data_patterns(self, text: str) -> bool:
        """
        检查文本是否包含明显的数据模式

        Args:
            text: 要检查的文本

        Returns:
            bool: 如果包含数据模式返回True
        """
        # 常见的数据模式
        data_patterns = [
            # 学科组合模式
            '语文', '数学', '英语', '物理', '化学', '生物', '历史', '地理', '政治',
            # 时间模式
            '上午', '下午', '晚上', '早上',
            # 其他常见数据模式
            '第一', '第二', '第三', '第四', '第五'
        ]

        # 检查是否包含多个学科或时间词汇（表明是数据而非表头）
        pattern_count = sum(1 for pattern in data_patterns if pattern in text)
        return pattern_count >= 2 or len(text) > 8

    def _determine_header_type(self, data: List[List]) -> Tuple[str, List[int]]:
        """
        确定表头类型

        改进的判断逻辑：
        1. 优先考虑第一行作为表头
        2. 对于多行候选，增加内容相关性和数据特征分析
        3. 避免将明显的数据行误判为表头行

        Args:
            data: Excel数据，每个元素是一行数据的列表

        Returns:
            Tuple[str, List[int]]: (表头类型, 表头行索引列表)
            表头类型包括：'无表头'、'单行表头'、'多行表头'
        """
        if not data:
            return '无表头', []

        header_rows = []  # 存储表头行的索引

        # 分析每一行是否为表头行，传入行索引用于位置权重计算
        for i, row in enumerate(data):
            if self._is_header_row(row, i):
                header_rows.append(i)

        # 根据表头行的分布确定表头类型
        if not header_rows:
            return '无表头', []
        elif len(header_rows) == 1:
            return '单行表头', header_rows
        else:
            # 检查表头行是否连续
            is_continuous = True
            for i in range(1, len(header_rows)):
                if header_rows[i] - header_rows[i - 1] != 1:
                    is_continuous = False
                    break

            # 如果表头行连续且从第一行开始，需要进一步验证是否真的是多行表头
            if is_continuous and header_rows[0] == 0:
                # 验证多行表头的合理性
                if self._validate_multi_row_header(data, header_rows):
                    return '多行表头', header_rows
                else:
                    # 如果验证失败，只取第一行作为单行表头
                    return '单行表头', [0]
            else:
                # 否则取第一个表头行作为单行表头
                return '单行表头', [header_rows[0]]

    def _extract_header_content(self, data: List[List], header_type: str, header_rows: List[int]) -> List[str]:
        """
        提取表头内容

        Args:
            data: Excel数据
            header_type: 表头类型
            header_rows: 表头行索引列表

        Returns:
            List[str]: 表头内容列表，每个元素对应一列的表头
        """
        if not header_rows or not data:
            return []

        # 获取列数
        max_cols = max(len(row) for row in data) if data else 0
        headers = []

        for col in range(max_cols):
            if header_type == '多行表头':
                # 多行表头：将多行内容用下划线连接
                col_headers = []
                for row_idx in header_rows:
                    if col < len(data[row_idx]):
                        value = data[row_idx][col]
                        if value is not None and str(value).strip():
                            col_headers.append(str(value).strip())

                # 用下划线连接非空的表头内容
                header_text = '_'.join(col_headers) if col_headers else f'列{col + 1}'
            else:
                # 单行表头：直接取对应行的内容
                row_idx = header_rows[0]
                if col < len(data[row_idx]):
                    value = data[row_idx][col]
                    header_text = str(value).strip() if value is not None else f'列{col + 1}'
                else:
                    header_text = f'列{col + 1}'

            headers.append(header_text)

        return headers

    def _get_first_data_row(self, data: List[List], header_rows: List[int]) -> List:
        """
        获取首行数据（非表头行的第一行）

        Args:
            data: Excel数据
            header_rows: 表头行索引列表

        Returns:
            List: 首行数据，如果没有数据行则返回空列表
        """
        if not data:
            return []

        # 找到第一个非表头行
        for i, row in enumerate(data):
            if i not in header_rows:
                return row

        # 如果所有行都是表头行，返回空列表
        return []

    def process_file(self, file_path: str) -> Dict[str, Any]:
        """
        处理单个Excel文件

        Args:
            file_path: Excel文件路径

        Returns:
            Dict[str, Any]: 处理结果字典，包含以下键：
            - file_name: 文件名
            - header_type: 表头类型
            - headers: 表头内容列表
            - first_data_row: 首行数据
            - error: 错误信息（如果有）
        """
        try:
            # 读取Excel文件数据
            data, total_cols = self._read_excel_with_merged_cells(file_path)

            if not data:
                return {
                    'file_name': os.path.basename(file_path),
                    'header_type': '读取失败',
                    'headers': [],
                    'first_data_row': [],
                    'error': '无法读取文件数据'
                }

            # 确定表头类型
            header_type, header_rows = self._determine_header_type(data)

            # 提取表头内容
            headers = self._extract_header_content(data, header_type, header_rows)

            # 获取首行数据
            first_data_row = self._get_first_data_row(data, header_rows)

            return {
                'file_name': os.path.basename(file_path),
                'header_type': header_type,
                'headers': headers,
                'first_data_row': first_data_row,
                'error': None
            }

        except Exception as e:
            return {
                'file_name': os.path.basename(file_path),
                'header_type': '处理失败',
                'headers': [],
                'first_data_row': [],
                'error': str(e)
            }

    def process_directory(self, directory_path: str) -> List[Dict[str, Any]]:
        """
        处理指定目录下的所有Excel文件

        Args:
            directory_path: 目录路径

        Returns:
            List[Dict[str, Any]]: 所有文件的处理结果列表
        """
        results = []

        try:
            # 检查目录是否存在
            if not os.path.exists(directory_path):
                print(f"目录不存在: {directory_path}")
                return results

            # 遍历目录中的所有文件
            for filename in os.listdir(directory_path):
                # 只处理Excel文件
                if filename.lower().endswith(('.xlsx', '.xls')):
                    file_path = os.path.join(directory_path, filename)
                    print(f"正在处理文件: {filename}")

                    # 处理单个文件
                    result = self.process_file(file_path)
                    results.append(result)

            return results

        except Exception as e:
            print(f"处理目录时出错: {str(e)}")
            return results




# 使用示例
if __name__ == "__main__":
    # 创建处理器实例
    processor = ExcelHeaderProcessor()

    # 指定要处理的目录路径
    directory_path = "C:/Users/<USER>/Desktop/excels/"  # excel目录路径

    # 处理目录中的所有Excel文件
    results = processor.process_directory(directory_path)

    # 输出处理结果
    for result in results:
        print(f"\n文件名: {result['file_name']}")
        print(f"表头类型: {result['header_type']}")
        print(f"表头内容: {result['headers']}")
        print(f"首行数据: {result['first_data_row']}")
        if result['error']:
            print(f"错误信息: {result['error']}")
        print("-" * 50)
