#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 简单测试修复效果
import sys
import os

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from main import ExcelHeaderProcessor
    
    processor = ExcelHeaderProcessor()
    
    # 测试数据：你的Excel表格
    test_data = [
        ['课程', '周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        ['上午', '语文数学', '地理英语', '美术音乐', '生物物理', '化学政治', '历史', '语文数学']
    ]
    
    print("测试数据：")
    print("第1行:", test_data[0])
    print("第2行:", test_data[1])
    
    # 测试表头类型判断
    header_type, header_rows = processor._determine_header_type(test_data)
    
    print(f"\n结果：")
    print(f"表头类型: {header_type}")
    print(f"表头行: {header_rows}")
    
    # 验证结果
    if header_type == '单行表头':
        print("✓ 修复成功！正确识别为单行表头")
    else:
        print("✗ 仍有问题，识别为:", header_type)
        
    # 测试单行判断
    print(f"\n单行分析：")
    is_header_1 = processor._is_header_row(test_data[0], 0)
    is_header_2 = processor._is_header_row(test_data[1], 1)
    print(f"第1行是表头: {is_header_1}")
    print(f"第2行是表头: {is_header_2}")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
