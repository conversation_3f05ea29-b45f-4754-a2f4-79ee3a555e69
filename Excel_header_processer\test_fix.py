#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的Excel表头处理器
用于验证单行表头和多行表头的判断逻辑是否正确
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import ExcelHeaderProcessor


def test_header_detection():
    """测试表头检测功能"""
    processor = ExcelHeaderProcessor()
    
    # 测试用例1：你提供的Excel表格数据（应该被识别为单行表头）
    test_data_1 = [
        ['课程', '周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        ['上午', '语文数学', '地理英语', '美术音乐', '生物物理', '化学政治', '历史', '语文数学']
    ]
    
    print("=== 测试用例1：课程表数据 ===")
    print("原始数据：")
    for i, row in enumerate(test_data_1):
        print(f"第{i+1}行: {row}")
    
    header_type_1, header_rows_1 = processor._determine_header_type(test_data_1)
    print(f"\n检测结果：")
    print(f"表头类型: {header_type_1}")
    print(f"表头行索引: {header_rows_1}")
    
    # 提取表头内容
    headers_1 = processor._extract_header_content(test_data_1, header_type_1, header_rows_1)
    print(f"表头内容: {headers_1}")
    
    # 获取首行数据
    first_data_1 = processor._get_first_data_row(test_data_1, header_rows_1)
    print(f"首行数据: {first_data_1}")
    
    # 测试用例2：真正的多行表头
    test_data_2 = [
        ['销售数据', '销售数据', '销售数据', '成本数据', '成本数据', '成本数据'],
        ['产品A', '产品B', '产品C', '产品A', '产品B', '产品C'],
        [100, 200, 150, 80, 120, 90]
    ]
    
    print("\n\n=== 测试用例2：真正的多行表头 ===")
    print("原始数据：")
    for i, row in enumerate(test_data_2):
        print(f"第{i+1}行: {row}")
    
    header_type_2, header_rows_2 = processor._determine_header_type(test_data_2)
    print(f"\n检测结果：")
    print(f"表头类型: {header_type_2}")
    print(f"表头行索引: {header_rows_2}")
    
    # 提取表头内容
    headers_2 = processor._extract_header_content(test_data_2, header_type_2, header_rows_2)
    print(f"表头内容: {headers_2}")
    
    # 获取首行数据
    first_data_2 = processor._get_first_data_row(test_data_2, header_rows_2)
    print(f"首行数据: {first_data_2}")
    
    # 测试用例3：单行表头
    test_data_3 = [
        ['姓名', '年龄', '性别', '职业'],
        ['张三', 25, '男', '工程师'],
        ['李四', 30, '女', '设计师']
    ]
    
    print("\n\n=== 测试用例3：简单单行表头 ===")
    print("原始数据：")
    for i, row in enumerate(test_data_3):
        print(f"第{i+1}行: {row}")
    
    header_type_3, header_rows_3 = processor._determine_header_type(test_data_3)
    print(f"\n检测结果：")
    print(f"表头类型: {header_type_3}")
    print(f"表头行索引: {header_rows_3}")
    
    # 提取表头内容
    headers_3 = processor._extract_header_content(test_data_3, header_type_3, header_rows_3)
    print(f"表头内容: {headers_3}")
    
    # 获取首行数据
    first_data_3 = processor._get_first_data_row(test_data_3, header_rows_3)
    print(f"首行数据: {first_data_3}")
    
    # 验证结果
    print("\n\n=== 验证结果 ===")
    print(f"测试用例1（课程表）- 期望：单行表头，实际：{header_type_1} {'✓' if header_type_1 == '单行表头' else '✗'}")
    print(f"测试用例2（多层数据）- 期望：多行表头，实际：{header_type_2} {'✓' if header_type_2 == '多行表头' else '✗'}")
    print(f"测试用例3（简单表格）- 期望：单行表头，实际：{header_type_3} {'✓' if header_type_3 == '单行表头' else '✗'}")


def test_individual_row_analysis():
    """测试单行分析功能"""
    processor = ExcelHeaderProcessor()
    
    print("\n\n=== 单行分析测试 ===")
    
    # 测试不同类型的行
    test_rows = [
        (['课程', '周一', '周二', '周三', '周四', '周五', '周六', '周日'], "表头行"),
        (['上午', '语文数学', '地理英语', '美术音乐', '生物物理', '化学政治', '历史', '语文数学'], "数据行"),
        (['姓名', '年龄', '性别', '职业'], "简单表头行"),
        (['张三', 25, '男', '工程师'], "简单数据行"),
        (['销售数据', '销售数据', '销售数据', '成本数据', '成本数据', '成本数据'], "多行表头第一行"),
        (['产品A', '产品B', '产品C', '产品A', '产品B', '产品C'], "多行表头第二行")
    ]
    
    for i, (row_data, description) in enumerate(test_rows):
        is_header = processor._is_header_row(row_data, i)
        characteristics = processor._analyze_row_characteristics(row_data)
        
        print(f"\n{description}:")
        print(f"  数据: {row_data}")
        print(f"  是否为表头: {is_header}")
        print(f"  特征分析: 空值率={characteristics['null_ratio']:.2f}, "
              f"数值率={characteristics['numeric_ratio']:.2f}, "
              f"文本率={characteristics['text_ratio']:.2f}")


if __name__ == "__main__":
    print("开始测试修复后的Excel表头处理器...")
    test_header_detection()
    test_individual_row_analysis()
    print("\n测试完成！")
