from main import ExcelHeaderProcessor

processor = ExcelHeaderProcessor()

# 测试你的Excel数据
test_data = [
    ['课程', '周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    ['上午', '语文数学', '地理英语', '美术音乐', '生物物理', '化学政治', '历史', '语文数学']
]

print('=== 测试课程表数据 ===')
for i, row in enumerate(test_data):
    print(f'第{i+1}行: {row}')

header_type, header_rows = processor._determine_header_type(test_data)
print('\n检测结果：')
print(f'表头类型: {header_type}')
print(f'表头行索引: {header_rows}')

headers = processor._extract_header_content(test_data, header_type, header_rows)
print(f'表头内容: {headers}')

first_data = processor._get_first_data_row(test_data, header_rows)
print(f'首行数据: {first_data}')

result = '✓ 正确' if header_type == '单行表头' else '✗ 错误'
print(f'\n结果验证: {result}')

# 测试单行分析
print('\n=== 单行分析 ===')
row1 = test_data[0]
row2 = test_data[1]

is_header_1 = processor._is_header_row(row1, 0)
is_header_2 = processor._is_header_row(row2, 1)

print(f'第1行是否为表头: {is_header_1}')
print(f'第2行是否为表头: {is_header_2}')
